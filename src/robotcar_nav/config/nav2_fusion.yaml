### Nav2详细配置文件 - 基于成功架构的完整版本 ###
# 适用场景: Cartographer定位 + Nav2导航
# 机器人类型: 差分驱动，矩形footprint (0.7m×0.56m)
# 传感器配置: 激光雷达 + IMU + 里程计
# 关键成功要素: 独立的代价地图节点管理 + 直接订阅Cartographer地图

# ========== 行为树导航器配置 ==========
# BT Navigator是Nav2的核心决策模块，负责执行导航任务的高级逻辑
bt_navigator:
  ros__parameters:
    use_sim_time: false                    # 使用真实时间，非仿真模式
    global_frame: map                      # 全局参考坐标系，来自Cartographer
    robot_base_frame: base_footprint      # 机器人本体坐标系（标准名称，命名空间由PushRosNamespace自动添加）
    odom_topic: odom                      # 里程计话题，来自EKF融合后的数据
    bt_loop_duration: 10                   # 行为树执行周期(ms)，控制决策频率
    default_server_timeout: 20             # 默认服务超时时间(秒)

    # Groot2监控配置
    enable_groot_monitoring: true          # 启用Groot2实时监控
    groot_zmq_publisher_port: 1666         # ZMQ发布端口，Groot2监听此端口
    groot_zmq_server_port: 1667            # ZMQ服务端口，用于交互式调试

    # 行为树XML文件配置 - 定义导航的决策逻辑
    default_nav_to_pose_bt_xml: "/opt/ros/humble/share/nav2_bt_navigator/behavior_trees/navigate_to_pose_w_replanning_and_recovery.xml"
    default_nav_through_poses_bt_xml: "/opt/ros/humble/share/nav2_bt_navigator/behavior_trees/navigate_through_poses_w_replanning_and_recovery.xml"
    
    # 行为树插件库 - 定义可用的行为树节点类型
    plugin_lib_names:
      # === 核心导航动作节点 ===
      - nav2_compute_path_to_pose_action_bt_node        # 计算到目标点的路径
      - nav2_compute_path_through_poses_action_bt_node  # 计算通过多个路径点的路径
      - nav2_smooth_path_action_bt_node                 # 路径平滑处理
      - nav2_follow_path_action_bt_node                 # 跟随路径执行
      # === 恢复行为动作节点 ===
      - nav2_back_up_action_bt_node                     # 后退恢复行为
      - nav2_spin_action_bt_node                        # 原地旋转恢复行为
      - nav2_wait_action_bt_node                        # 等待行为
      - nav2_clear_costmap_service_bt_node              # 清除代价地图服务
      # === 条件判断节点 ===
      - nav2_is_stuck_condition_bt_node                 # 判断是否卡住
      - nav2_goal_reached_condition_bt_node             # 判断是否到达目标
      - nav2_goal_updated_condition_bt_node             # 判断目标是否更新
      - nav2_globally_updated_goal_condition_bt_node    # 判断全局目标是否更新
      - nav2_is_path_valid_condition_bt_node            # 判断路径是否有效
      - nav2_initial_pose_received_condition_bt_node    # 判断是否接收到初始位姿
      - nav2_transform_available_condition_bt_node      # 判断TF变换是否可用
      - nav2_time_expired_condition_bt_node             # 判断时间是否超时
      - nav2_path_expiring_timer_condition              # 路径过期计时器条件
      - nav2_distance_traveled_condition_bt_node        # 判断行驶距离条件
      - nav2_is_battery_low_condition_bt_node           # 判断电池电量低
      - nav2_is_battery_charging_condition_bt_node      # 判断电池是否充电中
      # === 控制和选择节点 ===
      - nav2_reinitialize_global_localization_service_bt_node  # 重新初始化全局定位
      - nav2_rate_controller_bt_node                    # 速率控制器
      - nav2_distance_controller_bt_node                # 距离控制器
      - nav2_speed_controller_bt_node                   # 速度控制器
      - nav2_truncate_path_action_bt_node               # 路径截断动作
      - nav2_truncate_path_local_action_bt_node         # 局部路径截断动作
      - nav2_goal_updater_node_bt_node                  # 目标更新节点
      - nav2_recovery_node_bt_node                      # 恢复节点
      - nav2_pipeline_sequence_bt_node                  # 管道序列节点
      - nav2_round_robin_node_bt_node                   # 轮询节点
      - nav2_single_trigger_bt_node                     # 单次触发节点
      - nav2_goal_updated_controller_bt_node            # 目标更新控制器
      - nav2_navigate_through_poses_action_bt_node      # 多点导航动作
      - nav2_navigate_to_pose_action_bt_node            # 单点导航动作
      - nav2_remove_passed_goals_action_bt_node         # 移除已通过目标动作
      - nav2_planner_selector_bt_node                   # 规划器选择器
      - nav2_controller_selector_bt_node                # 控制器选择器
      - nav2_goal_checker_selector_bt_node              # 目标检查器选择器
      - nav2_controller_cancel_bt_node                  # 控制器取消节点
      - nav2_path_longer_on_approach_bt_node            # 接近时路径延长节点
      - nav2_wait_cancel_bt_node                        # 等待取消节点
      - nav2_spin_cancel_bt_node                        # 旋转取消节点
      - nav2_back_up_cancel_bt_node                     # 后退取消节点
      - nav2_assisted_teleop_cancel_bt_node             # 辅助遥控取消节点
      - nav2_drive_on_heading_cancel_bt_node            # 按航向行驶取消节点

# ========== 控制器服务器配置 ==========
# Controller Server负责局部路径跟踪和避障
controller_server:
  ros__parameters:
    use_sim_time: false                   # 使用真实时间
    odom_topic: odom                      # 里程计话题（相对路径）
    controller_frequency: 20.0            # 控制器频率(Hz)
    min_x_velocity_threshold: 0.001       # X方向最小速度阈值
    min_y_velocity_threshold: 0.0         # Y方向最小速度阈值
    min_theta_velocity_threshold: 0.001   # 角速度最小阈值
    failure_tolerance: 0.3                # 失败容忍度
    progress_checker_plugins: ["progress_checker"]
    goal_checker_plugins: ["goal_checker"]
    controller_plugins: ["FollowPath"]

    progress_checker:
      plugin: "nav2_controller::SimpleProgressChecker"
      required_movement_radius: 0.5
      movement_time_allowance: 10.0

    goal_checker:
      plugin: "nav2_controller::SimpleGoalChecker"
      xy_goal_tolerance: 0.25
      yaw_goal_tolerance: 0.25
      stateful: True
    FollowPath:
      plugin: "dwb_core::DWBLocalPlanner"

      # DWB控制器参数
      debug_trajectory_details: True

      # Critics配置 
      critics: ["RotateToGoal", "Oscillation", "BaseObstacle", "GoalAlign", "PathAlign", "PathDist", "GoalDist"]

      # 基本速度限制
      min_vel_x: 0.0
      min_vel_y: 0.0
      max_vel_x: 0.2
      max_vel_y: 0.0
      max_vel_theta: 0.2
      min_speed_xy: 0.0         #最小
      max_speed_xy: 0.35
      min_speed_theta: 0.0

      # 加速度限制
      acc_lim_x: 0.2
      acc_lim_y: 0.0
      acc_lim_theta: 0.2
      decel_lim_x: -0.2
      decel_lim_y: 0.0
      decel_lim_theta: -1.5

      # 采样参数
      vx_samples: 15
      vy_samples: 0
      vtheta_samples: 20
      sim_time: 2.0
      linear_granularity: 0.05
      angular_granularity: 0.025
      transform_tolerance: 0.5
      xy_goal_tolerance: 0.10
      trans_stopped_velocity: 0.08
      short_circuit_trajectory_evaluation: True
      stateful: True

      # Critics参数配置
      BaseObstacle.scale: 0.02
      PathAlign.scale: 32.0
      PathAlign.forward_point_distance: 0.1
      GoalAlign.scale: 24.0
      GoalAlign.forward_point_distance: 0.1
      PathDist.scale: 32.0
      GoalDist.scale: 24.0
      RotateToGoal.scale: 32.0
      RotateToGoal.slowing_factor: 5.0
      RotateToGoal.lookahead_time: -1.0

# ========== 局部代价地图配置 ==========
# 关键成功要素：作为独立节点在lifecycle_manager中管理
local_costmap:
  local_costmap:
    ros__parameters:
      # --- 基本参数配置 ---
      update_frequency: 20.0              # 地图更新频率(Hz)，高频率保证实时性
      publish_frequency: 20.0             # 地图发布频率(Hz)
      global_frame: odom                   # 全局参考坐标系(里程计坐标系，标准名称)
      robot_base_frame: base_footprint     # 机器人本体坐标系（标准名称）
      transform_timeout: 1.0               # TF变换超时时间(秒)，增加容错性
      tf_buffer_duration: 10.0             # TF缓存持续时间(秒)
      use_sim_time: false                  # 使用真实时间
      rolling_window: true                 # 启用滚动窗口，跟随机器人移动
      
      # --- 地图尺寸配置 ---
      width: 3                             # 地图宽度(米)
      height: 3                            # 地图高度(米)
      resolution: 0.03                     # 地图分辨率(m/pixel)，与建图分辨率一致
      
      # --- 机器人形状配置 ---
      # 使用矩形footprint，基于URDF尺寸0.75×0.6m + 安全边距
      footprint: "[[0.42, 0.35], [0.42, -0.35], [-0.42, -0.35], [-0.42, 0.35]]"
      
      # --- 插件配置 ---
      plugins: ["static_layer", "obstacle_layer", "inflation_layer"]  # {{ denoise_layer插件作为备选看看用不用. }}
      
      # === 静态层配置 ===
      # 成功关键：直接订阅Cartographer发布的/map话题
      static_layer:
        plugin: "nav2_costmap_2d::StaticLayer"
        map_subscribe_transient_local: True  # 订阅持久化地图话题
      
      # === 障碍物层配置 ===
      obstacle_layer:
        plugin: "nav2_costmap_2d::ObstacleLayer"
        enabled: True                      # 启用障碍物层
        observation_sources: scan          # 观测数据源
        footprint_clearing_enabled: true   # 启用机器人轮廓清除 
        combination_method: 1              # 组合方法：1取最大值 减少噪声影响
        scan:
          # {{ AURA-X: Modify - 修复话题路径为相对路径，支持命名空间. Approval: 寸止(ID:1735659600). }}
          topic: merged                    # 激光雷达话题（相对路径，支持命名空间）
          max_obstacle_height: 0.5         # 最大障碍物高度(m)
          clearing: True                   # 启用障碍物清除
          marking: True                    # 启用障碍物标记
          data_type: "LaserScan"           # 数据类型
          raytrace_max_range: 3.0          # 光线追踪最大范围(m)
          raytrace_min_range: 0.0          # 光线追踪最小范围(m)
          obstacle_max_range: 2.5          # 障碍物检测最大范围(m)
          obstacle_min_range: 0.0          # 障碍物检测最小范围(m)
      
      # {{ denoise_layer配置作为备选 }}

      # === 膨胀层配置 ===
      inflation_layer:
        plugin: "nav2_costmap_2d::InflationLayer"
        enabled: true                      # 启用膨胀层
        cost_scaling_factor: 3.0           # 代价缩放因子，影响膨胀梯度
        inflation_radius: 0.60             # 膨胀半径(m)，增加安全边距防止碰撞
      
      always_send_full_costmap: True       # 总是发送完整代价地图

# ========== 全局代价地图配置 ==========
# 关键成功要素：作为独立节点在lifecycle_manager中管理
global_costmap:
  global_costmap:
    ros__parameters:
      # --- 基本参数配置 ---
      update_frequency: 20.0              # 地图更新频率(Hz)，比局部地图低以节省计算
      publish_frequency: 20.0             # 地图发布频率(Hz)
      global_frame: map                    # 全局参考坐标系(地图坐标系)
      robot_base_frame: base_footprint     # 机器人本体坐标系（标准名称）
      transform_timeout: 1.0               # TF变换超时时间(秒)，增加容错性
      tf_buffer_duration: 10.0             # TF缓存持续时间(秒)
      use_sim_time: false                  # 使用真实时间
      
      # --- 机器人形状配置 ---
      # 与局部代价地图保持一致的矩形footprint
      footprint: "[[0.35, 0.28], [0.35, -0.28], [-0.35, -0.28], [-0.35, 0.28]]"
      
      # --- 地图参数配置 ---
      resolution: 0.03                     # 地图分辨率(m/pixel)，与Cartographer建图一致
      track_unknown_space: false           # 不跟踪未知空间，提高性能
      
      # --- 插件配置 ---
      plugins: ["static_layer", "obstacle_layer", "inflation_layer"]  # {{ AURA-X: Fix - 移除不存在的denoise_layer插件. }}

      # {{ AURA-X: Remove - 移除不存在的denoise_layer配置. }}
      # 全局代价地图：更严格的噪声过滤，提高全局路径规划的准确性
      # === 静态层配置 ===
      # 成功关键：直接订阅Cartographer发布的/map话题
      static_layer:
        plugin: "nav2_costmap_2d::StaticLayer"
        map_subscribe_transient_local: True  # 订阅持久化地图话题
      
      # === 障碍物层配置 ===
      # 与局部代价地图类似，但用于全局路径规划
      obstacle_layer:
        plugin: "nav2_costmap_2d::ObstacleLayer"
        enabled: True                      # 启用障碍物层
        observation_sources: scan          # 观测数据源
        scan:
          # {{ AURA-X: Modify - 修复话题路径为相对路径，支持命名空间. Approval: 寸止(ID:1735659600). }}
          topic: merged                    # 激光雷达话题（相对路径，支持命名空间）
          max_obstacle_height: 2.0         # 最大障碍物高度(m)
          clearing: True                   # 启用障碍物清除
          marking: True                    # 启用障碍物标记
          data_type: "LaserScan"           # 数据类型
          raytrace_max_range: 3.0          # 光线追踪最大范围(m)
          raytrace_min_range: 0.0          # 光线追踪最小范围(m)
          obstacle_max_range: 2.5          # 障碍物检测最大范围(m)
          obstacle_min_range: 0.0          # 障碍物检测最小范围(m)

      # === 膨胀层配置 ===
      # 全局膨胀半径比局部稍大，为长距离规划提供更多安全边距
      inflation_layer:
        plugin: "nav2_costmap_2d::InflationLayer"
        enabled: true                      # 启用膨胀层
        cost_scaling_factor: 3.0           # 代价缩放因子
        inflation_radius: 0.65             # 膨胀半径(m)，比局部地图大0.1m

      always_send_full_costmap: True       # 总是发送完整代价地图

# ========== 地图服务器配置 ==========
# Map Server负责加载和发布静态地图，由launch文件动态配置
map_server:
  ros__parameters:
    use_sim_time: false                   # 使用真实时间
    yaml_filename: ""                     # 将由launch文件动态设置

# ========== 规划器服务器配置 ==========
# Planner Server负责全局路径规划，从起点到终点生成最优路径
planner_server:
  ros__parameters:
    expected_planner_frequency: 10.0      # 期望的规划频率(Hz)
    use_sim_time: false                   # 使用真实时间
    planner_plugins: ["GridBased"]        # 规划器插件列表

    # === SMAC 2D规划器配置 ===
    # 适用于配合RotationShimController使用的2D规划算法
    # 不考虑运动学约束，生成更直接的路径，让RotationShimController处理旋转
    GridBased:
      plugin: "nav2_smac_planner/SmacPlanner2D"

      # --- 基本配置 ---
      tolerance: 0.1                      # 目标容差(m)，增加容差提高成功率
      downsample_costmap: false           # 不降采样代价地图，保持精度
      downsampling_factor: 1              # 降采样因子(未使用)
      allow_unknown: false                 # 允许通过未知区域，提高规划成功率
      max_iterations: 1000000             # 最大迭代次数
      max_on_approach_iterations: 1000    # 接近目标时的最大迭代次数
      max_planning_time: 3.0              # 最大规划时间(秒)

      # --- 运动模型配置 ---
      motion_model_for_search: "MOORE"    # 使用Moore邻域(8方向)，适合2D规划
      angle_quantization_bins: 72         # 角度量化分箱数(5度精度)

      # --- 代价惩罚配置 ---
      cost_penalty: 2.0                   # 代价惩罚，避免接近障碍物

      # --- 性能优化配置 ---
      use_final_approach_orientation: false  # 暂时关闭最终朝向要求，避免与RotationShim冲突
      smooth_path: true                   # 启用路径平滑

# ========== 路径平滑器服务器配置 ==========
# Smoother Server负责对规划的路径进行平滑处理，提高执行质量
smoother_server:
  ros__parameters:
    use_sim_time: false                   # 使用真实时间
    smoother_plugins: ["simple_smoother"]  # 使用简单平滑器

    # === 简单平滑器配置 ===
    # 基于简单算法的路径平滑，计算量小，适合实时应用
    simple_smoother:
      plugin: "nav2_smoother::SimpleSmoother"

      # --- 基本配置 ---
      tolerance: 1.0e-10                  # 收敛容差
      max_its: 1000                       # 最大迭代次数
      do_refinement: true                 # 启用路径细化

# ========== 行为服务器配置 ==========
# Behavior Server负责执行恢复行为，当导航失败时进行恢复操作
behavior_server:
  ros__parameters:
    # --- 基本配置 ---
    # {{ AURA-X: Fix - 补全behavior_server配置，对标pb2025和Nav2官方标准. Approval: 寸止(ID:1735659800). }}
    use_sim_time: false                   # 使用真实时间
    local_costmap_topic: local_costmap/costmap_raw     # 局部代价地图话题
    global_costmap_topic: global_costmap/costmap_raw   # 全局代价地图话题
    local_footprint_topic: local_costmap/published_footprint   # 局部机器人轮廓话题
    global_footprint_topic: global_costmap/published_footprint # 全局机器人轮廓话题
    cycle_frequency: 10.0                # 行为执行频率(Hz)
    behavior_plugins: ["spin", "backup", "drive_on_heading", "wait"]  # 行为插件列表

    # === 行为插件配置 ===
    spin:
      plugin: "nav2_behaviors/Spin"       # 原地旋转行为
    backup:
      plugin: "nav2_behaviors/BackUp"     # 后退行为
    drive_on_heading:
      plugin: "nav2_behaviors/DriveOnHeading"  # 按航向行驶行为
    wait:
      plugin: "nav2_behaviors/Wait"       # 等待行为

    # --- 行为参数配置 ---
    local_frame: odom                     # 局部坐标系（标准名称）
    global_frame: map                     # 全局坐标系（标准名称）
    robot_base_frame: base_footprint     # 机器人本体坐标系（标准名称）
    transform_tolerance: 0.4              # TF变换容差(秒)，与pb2025保持一致
    simulate_ahead_time: 2.0              # 前瞻仿真时间(秒)，与pb2025保持一致
    max_rotational_vel: 0.4                # 最大旋转速度(rad/s)，与pb2025保持一致
    min_rotational_vel: 0.2               # 最小旋转速度(rad/s)，与pb2025保持一致
    rotational_acc_lim: 0.6               # 旋转加速度限制(rad/s²)，与pb2025保持一致

# ========== 路径点跟随器配置 ==========
# Waypoint Follower负责执行多点导航任务
waypoint_follower:
  ros__parameters:
    use_sim_time: false                   # 使用真实时间
    loop_rate: 20                         # 循环频率(Hz)
    stop_on_failure: false                # 失败时不停止，继续下一个路径点
    waypoint_task_executor_plugin: "wait_at_waypoint"  # 路径点任务执行器插件

    # === 路径点等待任务配置 ===
    wait_at_waypoint:
      plugin: "nav2_waypoint_follower::WaitAtWaypoint"
      enabled: True                       # 启用等待任务
      waypoint_pause_duration: 2000      # 在每个路径点停留2秒(毫秒)

# ========== 速度平滑器配置 ==========
# Velocity Smoother负责对控制命令进行平滑处理，提高机器人运动质量
velocity_smoother:
  ros__parameters:
    use_sim_time: false                   # 使用真实时间
    smoothing_frequency: 20.0             # 平滑频率(Hz)
    scale_velocities: false               # 不缩放速度
    feedback: "OPEN_LOOP"                 # 开环反馈模式

    # --- 速度限制配置 ---
    max_velocity: [0.6, 0.0, 1.0]        # 最大速度[x, y, theta]，提高直线速度
    min_velocity: [0.0, 0.0, -1.0]       # 最小速度，禁止倒退
    max_accel: [0.4, 0.0, 0.8]           # 最大加速度[x, y, theta]，保持原值
    max_decel: [-0.4, 0.0, -0.8]         # 最大减速度[x, y, theta]，保持原值

    # --- 其他配置 ---
    odom_topic: "odom"                    # 里程计话题
    odom_duration: 0.1                    # 里程计持续时间(秒)
    deadband_velocity: [0.0, 0.0, 0.0]   # 死区速度
    velocity_timeout: 1.0                 # 速度超时时间(秒)

# ========== Cartographer节点配置 ==========
# {{ AURA-X: Add - 添加Cartographer节点参数配置，支持命名空间. }}
cartographer_node:
  ros__parameters:
    use_sim_time: false

# Cartographer占用栅格节点配置
cartographer_occupancy_grid_node:
  ros__parameters:
    use_sim_time: false

# ========== 生命周期管理器配置 ==========
# {{ AURA-X: Remove - lifecycle_manager配置移至启动文件，遵循pb2025和Nav2官方最佳实践. Approval: 寸止(ID:1735659800). }}
# 注意：lifecycle_manager现在在navigation_launch.py中配置，这是Nav2官方推荐的做法
# 这样可以确保节点列表与实际启动的节点保持同步
