#!/bin/bash
# Nav2导航系统启动脚本 - 支持控制器选择
# {{ AURA-X: Create - 支持多种控制器的导航启动脚本. }}

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 默认参数
USE_SIM_TIME="false"
START_RVIZ="true"
USE_EKF="true"
MAP_FILE="/home/<USER>/test_ws/src/robotcar_nav/maps/0624.pbstream"
NAMESPACE=""
CONTROLLER="dwb"

# 帮助信息
show_help() {
    echo -e "${BLUE}Nav2导航系统启动脚本${NC}"
    echo "=================================="
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -c, --controller CONTROLLER   选择控制器 (dwb/rpp/mppi/pb) [默认: dwb]"
    echo "  -m, --map MAP_FILE            地图文件路径"
    echo "  -n, --namespace NAMESPACE     ROS2命名空间"
    echo "  -s, --sim-time                使用仿真时间"
    echo "  --no-rviz                     不启动RViz"
    echo "  --no-ekf                      不使用EKF"
    echo "  -h, --help                    显示此帮助信息"
    echo ""
    echo "控制器说明:"
    echo "  dwb  - DWB Local Planner (默认)"
    echo "  rpp  - Regulated Pure Pursuit Controller"
    echo "  mppi - MPPI Controller"
    echo "  pb   - PB Omni PID Pursuit Controller"
    echo ""
    echo "示例:"
    echo "  $0                                    # 使用默认DWB控制器"
    echo "  $0 -c rpp                            # 使用Regulated Pure Pursuit控制器"
    echo "  $0 -c pb -n robot1                   # 使用PB控制器，命名空间为robot1"
    echo "  $0 -c mppi --no-rviz                 # 使用MPPI控制器，不启动RViz"
}

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        -c|--controller)
            CONTROLLER="$2"
            shift 2
            ;;
        -m|--map)
            MAP_FILE="$2"
            shift 2
            ;;
        -n|--namespace)
            NAMESPACE="$2"
            shift 2
            ;;
        -s|--sim-time)
            USE_SIM_TIME="true"
            shift
            ;;
        --no-rviz)
            START_RVIZ="false"
            shift
            ;;
        --no-ekf)
            USE_EKF="false"
            shift
            ;;
        -h|--help)
            show_help
            exit 0
            ;;
        *)
            echo -e "${RED}未知参数: $1${NC}"
            show_help
            exit 1
            ;;
    esac
done

# 验证控制器类型
case $CONTROLLER in
    dwb|rpp|mppi|pb)
        ;;
    *)
        echo -e "${RED}❌ 不支持的控制器类型: $CONTROLLER${NC}"
        echo -e "${YELLOW}💡 支持的控制器: dwb, rpp, mppi, pb${NC}"
        exit 1
        ;;
esac

# 检查地图文件
if [[ ! -f "$MAP_FILE" ]]; then
    echo -e "${RED}❌ 地图文件不存在: $MAP_FILE${NC}"
    exit 1
fi

# 显示启动配置
echo -e "${BLUE}🚀 Nav2导航系统启动配置${NC}"
echo "=================================="
echo -e "控制器:     ${GREEN}$CONTROLLER${NC}"
echo -e "地图文件:   ${GREEN}$MAP_FILE${NC}"
echo -e "命名空间:   ${GREEN}${NAMESPACE:-'(root)'}${NC}"
echo -e "仿真时间:   ${GREEN}$USE_SIM_TIME${NC}"
echo -e "启动RViz:   ${GREEN}$START_RVIZ${NC}"
echo -e "使用EKF:    ${GREEN}$USE_EKF${NC}"
echo ""

# 切换控制器配置
echo -e "${YELLOW}🔧 配置控制器...${NC}"
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
python3 "$SCRIPT_DIR/switch_controller.py" --switch "$CONTROLLER"

if [[ $? -ne 0 ]]; then
    echo -e "${RED}❌ 控制器配置失败${NC}"
    exit 1
fi

echo ""

# 构建启动参数
LAUNCH_ARGS=(
    "use_sim_time:=$USE_SIM_TIME"
    "start_rviz:=$START_RVIZ"
    "use_ekf:=$USE_EKF"
    "map_file:=$MAP_FILE"
)

# 添加命名空间参数（如果指定）
if [[ -n "$NAMESPACE" ]]; then
    LAUNCH_ARGS+=("namespace:=$NAMESPACE")
fi

# 根据控制器类型选择启动文件
case $CONTROLLER in
    pb)
        LAUNCH_ARGS+=("use_pb_controller:=true")
        ;;
    *)
        LAUNCH_ARGS+=("use_pb_controller:=false")
        ;;
esac

# 启动导航系统
echo -e "${GREEN}🚀 启动Nav2导航系统...${NC}"
echo -e "${BLUE}启动命令:${NC}"
echo "ros2 launch robotcar_laser_fusion fusion_05_navigation.launch.py ${LAUNCH_ARGS[*]}"
echo ""

# 等待用户确认
read -p "按Enter键继续启动，或Ctrl+C取消..."

# 执行启动命令
cd "$(dirname "$SCRIPT_DIR")"  # 切换到工作空间根目录
exec ros2 launch robotcar_laser_fusion fusion_05_navigation.launch.py "${LAUNCH_ARGS[@]}"
