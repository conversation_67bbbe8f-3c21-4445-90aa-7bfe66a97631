#!/usr/bin/env python3
"""
YAML路点配置到GeoJSON路网图转换工具
将fusion项目的waypoints.yaml格式转换为Nav2 Route Server支持的GeoJSON格式

{{ AURA-X: Create - YAML到GeoJSON转换工具，实现数据格式兼容. Approval: 寸止(ID:route_integration_phase1). }}
"""

import json
import yaml
import argparse
import sys
from pathlib import Path
from typing import Dict, List, Any
import math


class WaypointToRouteConverter:
    """路点配置到路网图转换器"""
    
    def __init__(self):
        self.waypoints = {}
        self.network_config = {}
    
    def load_yaml_config(self, yaml_file: str) -> bool:
        """加载YAML配置文件"""
        try:
            with open(yaml_file, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            
            if 'waypoints' not in config:
                print(f"❌ YAML文件中缺少waypoints配置: {yaml_file}")
                return False
            
            self.waypoints = {wp['name']: wp for wp in config['waypoints']}
            self.network_config = config.get('network', {})
            
            print(f"✅ 成功加载 {len(self.waypoints)} 个路点")
            return True
            
        except Exception as e:
            print(f"❌ 加载YAML文件失败: {e}")
            return False
    
    def create_geojson_graph(self) -> Dict[str, Any]:
        """创建GeoJSON格式的路网图"""
        
        # GeoJSON基础结构
        geojson = {
            "type": "FeatureCollection",
            "features": []
        }
        
        # 创建节点特征
        node_features = self._create_node_features()
        geojson["features"].extend(node_features)
        
        # 创建边特征
        edge_features = self._create_edge_features()
        geojson["features"].extend(edge_features)
        
        return geojson
    
    def _create_node_features(self) -> List[Dict[str, Any]]:
        """创建节点特征"""
        features = []
        
        for name, waypoint in self.waypoints.items():
            pos = waypoint['position']
            
            feature = {
                "type": "Feature",
                "geometry": {
                    "type": "Point",
                    "coordinates": [pos['x'], pos['y']]  # GeoJSON使用[x, y]格式
                },
                "properties": {
                    "id": name,
                    "name": name,
                    "description": waypoint.get('description', ''),
                    "node_type": "waypoint",
                    "z": pos.get('z', 0.0),
                    # 存储四元数方向信息
                    "orientation": {
                        "x": waypoint['orientation']['x'],
                        "y": waypoint['orientation']['y'], 
                        "z": waypoint['orientation']['z'],
                        "w": waypoint['orientation']['w']
                    }
                }
            }
            
            features.append(feature)
        
        return features
    
    def _create_edge_features(self) -> List[Dict[str, Any]]:
        """创建边特征"""
        features = []
        edge_id = 0
        
        for name, waypoint in self.waypoints.items():
            connections = waypoint.get('connections', [])
            
            for target_name in connections:
                if target_name not in self.waypoints:
                    print(f"⚠️  警告: 路点 {name} 连接到不存在的路点 {target_name}")
                    continue
                
                # 计算边的几何信息
                start_pos = waypoint['position']
                end_pos = self.waypoints[target_name]['position']
                
                # 计算距离和其他属性
                distance = self._calculate_distance(start_pos, end_pos)
                
                feature = {
                    "type": "Feature",
                    "geometry": {
                        "type": "LineString",
                        "coordinates": [
                            [start_pos['x'], start_pos['y']],
                            [end_pos['x'], end_pos['y']]
                        ]
                    },
                    "properties": {
                        "id": f"edge_{edge_id}",
                        "start_node": name,
                        "end_node": target_name,
                        "distance": distance,
                        "bidirectional": True,  # 默认双向
                        # 从网络配置中获取默认参数
                        "speed_limit": self.network_config.get('max_speed', 0.5),
                        "edge_type": "corridor",
                        # 语义信息
                        "semantic_class": self._infer_semantic_class(name, target_name),
                        # 权重信息
                        "weight": distance,  # 默认使用距离作为权重
                        "penalty": 0.0       # 默认无惩罚
                    }
                }
                
                features.append(feature)
                edge_id += 1
        
        return features
    
    def _calculate_distance(self, pos1: Dict, pos2: Dict) -> float:
        """计算两点间的欧几里得距离"""
        dx = pos2['x'] - pos1['x']
        dy = pos2['y'] - pos1['y']
        dz = pos2.get('z', 0.0) - pos1.get('z', 0.0)
        return math.sqrt(dx*dx + dy*dy + dz*dz)
    
    def _infer_semantic_class(self, start_name: str, end_name: str) -> str:
        """根据路点名称推断语义类别"""
        # 简单的语义推断逻辑
        if 'corridor' in start_name.lower() or 'corridor' in end_name.lower():
            return 'corridor'
        elif 'office' in start_name.lower() or 'office' in end_name.lower():
            return 'office_access'
        elif 'entrance' in start_name.lower() or 'entrance' in end_name.lower():
            return 'entrance'
        elif 'charging' in start_name.lower() or 'charging' in end_name.lower():
            return 'service'
        else:
            return 'general'
    
    def save_geojson(self, geojson_data: Dict[str, Any], output_file: str) -> bool:
        """保存GeoJSON文件"""
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(geojson_data, f, indent=2, ensure_ascii=False)
            
            print(f"✅ GeoJSON路网图已保存到: {output_file}")
            return True
            
        except Exception as e:
            print(f"❌ 保存GeoJSON文件失败: {e}")
            return False
    
    def print_conversion_summary(self, geojson_data: Dict[str, Any]):
        """打印转换摘要"""
        nodes = [f for f in geojson_data['features'] if f['geometry']['type'] == 'Point']
        edges = [f for f in geojson_data['features'] if f['geometry']['type'] == 'LineString']
        
        print("\n📊 转换摘要:")
        print(f"  节点数量: {len(nodes)}")
        print(f"  边数量: {len(edges)}")
        
        # 统计语义类别
        semantic_classes = {}
        for edge in edges:
            cls = edge['properties']['semantic_class']
            semantic_classes[cls] = semantic_classes.get(cls, 0) + 1
        
        print("  语义类别分布:")
        for cls, count in semantic_classes.items():
            print(f"    {cls}: {count}")


def main():
    parser = argparse.ArgumentParser(
        description="将YAML路点配置转换为GeoJSON路网图",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  python3 yaml_to_geojson_converter.py waypoints.yaml route_graph.geojson
  python3 yaml_to_geojson_converter.py config/waypoints_example.yaml config/route_graph.geojson
        """
    )
    
    parser.add_argument('input_yaml', help='输入的YAML路点配置文件')
    parser.add_argument('output_geojson', help='输出的GeoJSON路网图文件')
    parser.add_argument('--verbose', '-v', action='store_true', help='详细输出')
    
    args = parser.parse_args()
    
    # 检查输入文件
    if not Path(args.input_yaml).exists():
        print(f"❌ 输入文件不存在: {args.input_yaml}")
        sys.exit(1)
    
    # 创建转换器
    converter = WaypointToRouteConverter()
    
    # 加载YAML配置
    if not converter.load_yaml_config(args.input_yaml):
        sys.exit(1)
    
    # 转换为GeoJSON
    print("🔄 正在转换为GeoJSON格式...")
    geojson_data = converter.create_geojson_graph()
    
    # 保存结果
    if not converter.save_geojson(geojson_data, args.output_geojson):
        sys.exit(1)
    
    # 打印摘要
    if args.verbose:
        converter.print_conversion_summary(geojson_data)
    
    print("✅ 转换完成!")


if __name__ == "__main__":
    main()
