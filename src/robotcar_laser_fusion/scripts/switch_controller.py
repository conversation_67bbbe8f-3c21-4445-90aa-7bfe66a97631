#!/usr/bin/env python3
"""
Nav2控制器切换工具
用于在不同的Nav2控制器之间快速切换

{{ AURA-X: Create - Nav2控制器切换工具，支持多种控制器选择. }}
"""

import os
import sys
import argparse
import yaml
from pathlib import Path


class ControllerSwitcher:
    """Nav2控制器切换器"""
    
    def __init__(self):
        self.config_dir = Path(__file__).parent.parent.parent / "robotcar_nav" / "config"
        self.base_config = self.config_dir / "nav2_fusion.yaml"
        
        # 支持的控制器配置
        self.controllers = {
            'dwb': {
                'plugin': 'dwb_core::DWBLocalPlanner',
                'name': 'DWB Local Planner',
                'description': '动态窗口方法，适用于复杂环境导航',
                'config_template': 'dwb_config'
            },
            'rpp': {
                'plugin': 'nav2_regulated_pure_pursuit_controller::RegulatedPurePursuitController',
                'name': 'Regulated Pure Pursuit',
                'description': '纯追踪控制器，适用于差分驱动机器人',
                'config_template': 'rpp_config'
            },
            'mppi': {
                'plugin': 'nav2_mppi_controller::MPPIController',
                'name': 'MPPI Controller',
                'description': '模型预测路径积分控制器，适用于高动态环境',
                'config_template': 'mppi_config'
            },
            'pb': {
                'plugin': 'pb_omni_pid_pursuit_controller::OmniPidPursuitController',
                'name': 'PB Omni PID Pursuit',
                'description': 'PB项目的全向PID追踪控制器',
                'config_template': 'pb_config'
            }
        }
    
    def list_controllers(self):
        """列出所有可用的控制器"""
        print("🎮 可用的Nav2控制器:")
        print("=" * 60)
        for key, controller in self.controllers.items():
            print(f"📌 {key:4} - {controller['name']}")
            print(f"     插件: {controller['plugin']}")
            print(f"     描述: {controller['description']}")
            print()
    
    def get_current_controller(self):
        """获取当前使用的控制器"""
        try:
            with open(self.base_config, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            
            plugin = config['controller_server']['ros__parameters']['FollowPath']['plugin']
            
            # 查找匹配的控制器
            for key, controller in self.controllers.items():
                if controller['plugin'] == plugin:
                    return key, controller['name']
            
            return 'unknown', plugin
            
        except Exception as e:
            print(f"❌ 读取配置文件失败: {e}")
            return None, None
    
    def switch_controller(self, controller_type):
        """切换控制器"""
        if controller_type not in self.controllers:
            print(f"❌ 不支持的控制器类型: {controller_type}")
            print("💡 使用 --list 查看支持的控制器")
            return False
        
        controller = self.controllers[controller_type]
        
        try:
            # 读取当前配置
            with open(self.base_config, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            
            # 修改控制器插件
            config['controller_server']['ros__parameters']['FollowPath']['plugin'] = controller['plugin']
            
            # 根据控制器类型添加特定配置
            if controller_type == 'rpp':
                self._add_rpp_config(config)
            elif controller_type == 'mppi':
                self._add_mppi_config(config)
            elif controller_type == 'dwb':
                self._add_dwb_config(config)
            elif controller_type == 'pb':
                self._add_pb_config(config)
            
            # 备份原配置
            backup_file = self.base_config.with_suffix('.yaml.backup')
            if not backup_file.exists():
                with open(backup_file, 'w', encoding='utf-8') as f:
                    with open(self.base_config, 'r', encoding='utf-8') as orig:
                        f.write(orig.read())
                print(f"📋 已创建配置备份: {backup_file}")
            
            # 写入新配置
            with open(self.base_config, 'w', encoding='utf-8') as f:
                yaml.dump(config, f, default_flow_style=False, allow_unicode=True, indent=2)
            
            print(f"✅ 成功切换到 {controller['name']}")
            print(f"📝 配置文件已更新: {self.base_config}")
            print("🔄 请重启导航系统以应用更改")
            
            return True
            
        except Exception as e:
            print(f"❌ 切换控制器失败: {e}")
            return False
    
    def _add_rpp_config(self, config):
        """添加Regulated Pure Pursuit配置"""
        follow_path = config['controller_server']['ros__parameters']['FollowPath']
        
        # 清除DWB特定配置
        dwb_keys = ['critics', 'vx_samples', 'vy_samples', 'vtheta_samples', 'sim_time']
        for key in dwb_keys:
            follow_path.pop(key, None)
        
        # 添加RPP配置
        rpp_config = {
            'desired_linear_vel': 0.5,
            'lookahead_dist': 0.6,
            'min_lookahead_dist': 0.3,
            'max_lookahead_dist': 0.9,
            'lookahead_time': 1.5,
            'rotate_to_heading_angular_vel': 1.8,
            'transform_tolerance': 0.1,
            'use_velocity_scaled_lookahead_dist': False,
            'min_approach_linear_velocity': 0.05,
            'approach_velocity_scaling_dist': 0.6,
            'use_interpolation': True,
            'use_collision_detection': True,
            'max_allowed_time_to_collision_up_to_carrot': 1.0,
            'use_regulated_linear_velocity_scaling': True,
            'use_cost_regulated_linear_velocity_scaling': False,
            'regulated_linear_scaling_min_radius': 0.9,
            'regulated_linear_scaling_min_speed': 0.25,
            'use_rotate_to_heading': True,
            'rotate_to_heading_min_angle': 0.785,
            'max_angular_accel': 3.2,
            'max_robot_pose_search_dist': 10.0,
            'allow_reversing': False
        }
        
        follow_path.update(rpp_config)
    
    def _add_dwb_config(self, config):
        """添加DWB配置"""
        follow_path = config['controller_server']['ros__parameters']['FollowPath']
        
        # 清除其他控制器特定配置
        rpp_keys = ['desired_linear_vel', 'lookahead_dist', 'min_lookahead_dist']
        for key in rpp_keys:
            follow_path.pop(key, None)
        
        # 添加DWB配置
        dwb_config = {
            'debug_trajectory_details': True,
            'critics': ["RotateToGoal", "Oscillation", "BaseObstacle", "GoalAlign", "PathAlign", "PathDist", "GoalDist"],
            'min_vel_x': 0.0,
            'min_vel_y': 0.0,
            'max_vel_x': 0.4,
            'max_vel_y': 0.0,
            'max_vel_theta': 0.4,
            'min_speed_xy': 0.0,
            'max_speed_xy': 0.6,
            'min_speed_theta': 0.0,
            'acc_lim_x': 0.3,
            'acc_lim_y': 0.0,
            'acc_lim_theta': 0.6,
            'decel_lim_x': -0.5,
            'decel_lim_y': 0.0,
            'decel_lim_theta': -1.5,
            'vx_samples': 15,
            'vy_samples': 0,
            'vtheta_samples': 20,
            'sim_time': 2.0,
            'linear_granularity': 0.05,
            'angular_granularity': 0.025,
            'transform_tolerance': 0.5,
            'xy_goal_tolerance': 0.10,
            'trans_stopped_velocity': 0.08,
            'short_circuit_trajectory_evaluation': True,
            'stateful': True
        }
        
        follow_path.update(dwb_config)
    
    def _add_mppi_config(self, config):
        """添加MPPI配置"""
        follow_path = config['controller_server']['ros__parameters']['FollowPath']
        
        # 添加MPPI基础配置
        mppi_config = {
            'time_steps': 56,
            'model_dt': 0.05,
            'batch_size': 2000,
            'vx_std': 0.2,
            'vy_std': 0.2,
            'wz_std': 0.4,
            'vx_max': 0.5,
            'vx_min': -0.35,
            'vy_max': 0.5,
            'wz_max': 1.9,
            'iteration_count': 1,
            'prune_distance': 1.7,
            'transform_tolerance': 0.1,
            'temperature': 0.3,
            'gamma': 0.015,
            'motion_model': "DiffDrive",
            'visualize': False,
            'reset_period': 1.0
        }
        
        follow_path.update(mppi_config)
    
    def _add_pb_config(self, config):
        """添加PB控制器配置"""
        follow_path = config['controller_server']['ros__parameters']['FollowPath']
        
        # 添加PB控制器配置
        pb_config = {
            'kp_linear': 0.8,
            'ki_linear': 0.0,
            'kd_linear': 0.1,
            'kp_angular': 1.2,
            'ki_angular': 0.0,
            'kd_angular': 0.15,
            'max_linear_velocity': 0.5,
            'max_angular_velocity': 1.0,
            'lookahead_distance': 0.6,
            'min_lookahead_distance': 0.3,
            'max_lookahead_distance': 1.0,
            'transform_tolerance': 0.1,
            'goal_tolerance': 0.2
        }
        
        follow_path.update(pb_config)
    
    def restore_backup(self):
        """恢复备份配置"""
        backup_file = self.base_config.with_suffix('.yaml.backup')
        
        if not backup_file.exists():
            print("❌ 未找到备份文件")
            return False
        
        try:
            with open(backup_file, 'r', encoding='utf-8') as f:
                backup_content = f.read()
            
            with open(self.base_config, 'w', encoding='utf-8') as f:
                f.write(backup_content)
            
            print("✅ 已恢复备份配置")
            print("🔄 请重启导航系统以应用更改")
            return True
            
        except Exception as e:
            print(f"❌ 恢复备份失败: {e}")
            return False


def main():
    parser = argparse.ArgumentParser(
        description="Nav2控制器切换工具",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  python3 switch_controller.py --list                    # 列出所有控制器
  python3 switch_controller.py --current                 # 查看当前控制器
  python3 switch_controller.py --switch rpp              # 切换到Regulated Pure Pursuit
  python3 switch_controller.py --switch dwb              # 切换到DWB
  python3 switch_controller.py --restore                 # 恢复备份配置
        """
    )
    
    parser.add_argument('--list', action='store_true', help='列出所有可用的控制器')
    parser.add_argument('--current', action='store_true', help='显示当前使用的控制器')
    parser.add_argument('--switch', type=str, help='切换到指定的控制器 (dwb/rpp/mppi/pb)')
    parser.add_argument('--restore', action='store_true', help='恢复备份配置')
    
    args = parser.parse_args()
    
    if len(sys.argv) == 1:
        parser.print_help()
        return
    
    switcher = ControllerSwitcher()
    
    if args.list:
        switcher.list_controllers()
    elif args.current:
        controller_type, controller_name = switcher.get_current_controller()
        if controller_type:
            print(f"🎮 当前控制器: {controller_name} ({controller_type})")
        else:
            print("❌ 无法获取当前控制器信息")
    elif args.switch:
        switcher.switch_controller(args.switch)
    elif args.restore:
        switcher.restore_backup()


if __name__ == "__main__":
    main()
