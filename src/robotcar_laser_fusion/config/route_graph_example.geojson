{"type": "FeatureCollection", "features": [{"type": "Feature", "geometry": {"type": "Point", "coordinates": [0.0, 0.0]}, "properties": {"id": "entrance", "name": "entrance", "description": "入口区域", "node_type": "waypoint", "z": 0.0, "orientation": {"x": 0.0, "y": 0.0, "z": 0.0, "w": 1.0}}}, {"type": "Feature", "geometry": {"type": "Point", "coordinates": [2.0, 1.0]}, "properties": {"id": "reception", "name": "reception", "description": "接待区", "node_type": "waypoint", "z": 0.0, "orientation": {"x": 0.0, "y": 0.0, "z": 0.707, "w": 0.707}}}, {"type": "Feature", "geometry": {"type": "Point", "coordinates": [5.0, 0.0]}, "properties": {"id": "corridor_1", "name": "corridor_1", "description": "主走廊1", "node_type": "waypoint", "z": 0.0, "orientation": {"x": 0.0, "y": 0.0, "z": 0.0, "w": 1.0}}}, {"type": "Feature", "geometry": {"type": "Point", "coordinates": [10.0, 0.0]}, "properties": {"id": "corridor_2", "name": "corridor_2", "description": "主走廊2", "node_type": "waypoint", "z": 0.0, "orientation": {"x": 0.0, "y": 0.0, "z": 0.0, "w": 1.0}}}, {"type": "Feature", "geometry": {"type": "Point", "coordinates": [3.0, 3.0]}, "properties": {"id": "office_1", "name": "office_1", "description": "办公室1", "node_type": "waypoint", "z": 0.0, "orientation": {"x": 0.0, "y": 0.0, "z": 1.0, "w": 0.0}}}, {"type": "Feature", "geometry": {"type": "Point", "coordinates": [7.0, 3.0]}, "properties": {"id": "office_2", "name": "office_2", "description": "办公室2", "node_type": "waypoint", "z": 0.0, "orientation": {"x": 0.0, "y": 0.0, "z": 1.0, "w": 0.0}}}, {"type": "Feature", "geometry": {"type": "Point", "coordinates": [8.0, -3.0]}, "properties": {"id": "office_3", "name": "office_3", "description": "办公室3", "node_type": "waypoint", "z": 0.0, "orientation": {"x": 0.0, "y": 0.0, "z": 0.0, "w": 1.0}}}, {"type": "Feature", "geometry": {"type": "Point", "coordinates": [12.0, -3.0]}, "properties": {"id": "office_4", "name": "office_4", "description": "办公室4", "node_type": "waypoint", "z": 0.0, "orientation": {"x": 0.0, "y": 0.0, "z": 0.0, "w": 1.0}}}, {"type": "Feature", "geometry": {"type": "Point", "coordinates": [13.0, 2.0]}, "properties": {"id": "meeting_room", "name": "meeting_room", "description": "会议室", "node_type": "waypoint", "z": 0.0, "orientation": {"x": 0.0, "y": 0.0, "z": 0.707, "w": 0.707}}}, {"type": "Feature", "geometry": {"type": "Point", "coordinates": [1.0, -2.0]}, "properties": {"id": "charging_station", "name": "charging_station", "description": "充电站", "node_type": "waypoint", "z": 0.0, "orientation": {"x": 0.0, "y": 0.0, "z": -0.707, "w": 0.707}}}, {"type": "Feature", "geometry": {"type": "LineString", "coordinates": [[0.0, 0.0], [5.0, 0.0]]}, "properties": {"id": "edge_0", "start_node": "entrance", "end_node": "corridor_1", "distance": 5.0, "bidirectional": true, "speed_limit": 0.5, "edge_type": "corridor", "semantic_class": "corridor", "weight": 5.0, "penalty": 0.0}}, {"type": "Feature", "geometry": {"type": "LineString", "coordinates": [[0.0, 0.0], [2.0, 1.0]]}, "properties": {"id": "edge_1", "start_node": "entrance", "end_node": "reception", "distance": 2.23606797749979, "bidirectional": true, "speed_limit": 0.5, "edge_type": "corridor", "semantic_class": "entrance", "weight": 2.23606797749979, "penalty": 0.0}}, {"type": "Feature", "geometry": {"type": "LineString", "coordinates": [[2.0, 1.0], [0.0, 0.0]]}, "properties": {"id": "edge_2", "start_node": "reception", "end_node": "entrance", "distance": 2.23606797749979, "bidirectional": true, "speed_limit": 0.5, "edge_type": "corridor", "semantic_class": "entrance", "weight": 2.23606797749979, "penalty": 0.0}}, {"type": "Feature", "geometry": {"type": "LineString", "coordinates": [[2.0, 1.0], [5.0, 0.0]]}, "properties": {"id": "edge_3", "start_node": "reception", "end_node": "corridor_1", "distance": 3.1622776601683795, "bidirectional": true, "speed_limit": 0.5, "edge_type": "corridor", "semantic_class": "corridor", "weight": 3.1622776601683795, "penalty": 0.0}}, {"type": "Feature", "geometry": {"type": "LineString", "coordinates": [[2.0, 1.0], [3.0, 3.0]]}, "properties": {"id": "edge_4", "start_node": "reception", "end_node": "office_1", "distance": 2.23606797749979, "bidirectional": true, "speed_limit": 0.5, "edge_type": "corridor", "semantic_class": "office_access", "weight": 2.23606797749979, "penalty": 0.0}}, {"type": "Feature", "geometry": {"type": "LineString", "coordinates": [[5.0, 0.0], [0.0, 0.0]]}, "properties": {"id": "edge_5", "start_node": "corridor_1", "end_node": "entrance", "distance": 5.0, "bidirectional": true, "speed_limit": 0.5, "edge_type": "corridor", "semantic_class": "corridor", "weight": 5.0, "penalty": 0.0}}, {"type": "Feature", "geometry": {"type": "LineString", "coordinates": [[5.0, 0.0], [2.0, 1.0]]}, "properties": {"id": "edge_6", "start_node": "corridor_1", "end_node": "reception", "distance": 3.1622776601683795, "bidirectional": true, "speed_limit": 0.5, "edge_type": "corridor", "semantic_class": "corridor", "weight": 3.1622776601683795, "penalty": 0.0}}, {"type": "Feature", "geometry": {"type": "LineString", "coordinates": [[5.0, 0.0], [10.0, 0.0]]}, "properties": {"id": "edge_7", "start_node": "corridor_1", "end_node": "corridor_2", "distance": 5.0, "bidirectional": true, "speed_limit": 0.5, "edge_type": "corridor", "semantic_class": "corridor", "weight": 5.0, "penalty": 0.0}}, {"type": "Feature", "geometry": {"type": "LineString", "coordinates": [[5.0, 0.0], [3.0, 3.0]]}, "properties": {"id": "edge_8", "start_node": "corridor_1", "end_node": "office_1", "distance": 3.605551275463989, "bidirectional": true, "speed_limit": 0.5, "edge_type": "corridor", "semantic_class": "corridor", "weight": 3.605551275463989, "penalty": 0.0}}, {"type": "Feature", "geometry": {"type": "LineString", "coordinates": [[5.0, 0.0], [7.0, 3.0]]}, "properties": {"id": "edge_9", "start_node": "corridor_1", "end_node": "office_2", "distance": 3.605551275463989, "bidirectional": true, "speed_limit": 0.5, "edge_type": "corridor", "semantic_class": "corridor", "weight": 3.605551275463989, "penalty": 0.0}}, {"type": "Feature", "geometry": {"type": "LineString", "coordinates": [[10.0, 0.0], [5.0, 0.0]]}, "properties": {"id": "edge_10", "start_node": "corridor_2", "end_node": "corridor_1", "distance": 5.0, "bidirectional": true, "speed_limit": 0.5, "edge_type": "corridor", "semantic_class": "corridor", "weight": 5.0, "penalty": 0.0}}, {"type": "Feature", "geometry": {"type": "LineString", "coordinates": [[10.0, 0.0], [8.0, -3.0]]}, "properties": {"id": "edge_11", "start_node": "corridor_2", "end_node": "office_3", "distance": 3.605551275463989, "bidirectional": true, "speed_limit": 0.5, "edge_type": "corridor", "semantic_class": "corridor", "weight": 3.605551275463989, "penalty": 0.0}}, {"type": "Feature", "geometry": {"type": "LineString", "coordinates": [[10.0, 0.0], [12.0, -3.0]]}, "properties": {"id": "edge_12", "start_node": "corridor_2", "end_node": "office_4", "distance": 3.605551275463989, "bidirectional": true, "speed_limit": 0.5, "edge_type": "corridor", "semantic_class": "corridor", "weight": 3.605551275463989, "penalty": 0.0}}, {"type": "Feature", "geometry": {"type": "LineString", "coordinates": [[10.0, 0.0], [13.0, 2.0]]}, "properties": {"id": "edge_13", "start_node": "corridor_2", "end_node": "meeting_room", "distance": 3.605551275463989, "bidirectional": true, "speed_limit": 0.5, "edge_type": "corridor", "semantic_class": "corridor", "weight": 3.605551275463989, "penalty": 0.0}}, {"type": "Feature", "geometry": {"type": "LineString", "coordinates": [[3.0, 3.0], [2.0, 1.0]]}, "properties": {"id": "edge_14", "start_node": "office_1", "end_node": "reception", "distance": 2.23606797749979, "bidirectional": true, "speed_limit": 0.5, "edge_type": "corridor", "semantic_class": "office_access", "weight": 2.23606797749979, "penalty": 0.0}}, {"type": "Feature", "geometry": {"type": "LineString", "coordinates": [[3.0, 3.0], [5.0, 0.0]]}, "properties": {"id": "edge_15", "start_node": "office_1", "end_node": "corridor_1", "distance": 3.605551275463989, "bidirectional": true, "speed_limit": 0.5, "edge_type": "corridor", "semantic_class": "corridor", "weight": 3.605551275463989, "penalty": 0.0}}, {"type": "Feature", "geometry": {"type": "LineString", "coordinates": [[7.0, 3.0], [5.0, 0.0]]}, "properties": {"id": "edge_16", "start_node": "office_2", "end_node": "corridor_1", "distance": 3.605551275463989, "bidirectional": true, "speed_limit": 0.5, "edge_type": "corridor", "semantic_class": "corridor", "weight": 3.605551275463989, "penalty": 0.0}}, {"type": "Feature", "geometry": {"type": "LineString", "coordinates": [[8.0, -3.0], [10.0, 0.0]]}, "properties": {"id": "edge_17", "start_node": "office_3", "end_node": "corridor_2", "distance": 3.605551275463989, "bidirectional": true, "speed_limit": 0.5, "edge_type": "corridor", "semantic_class": "corridor", "weight": 3.605551275463989, "penalty": 0.0}}, {"type": "Feature", "geometry": {"type": "LineString", "coordinates": [[12.0, -3.0], [10.0, 0.0]]}, "properties": {"id": "edge_18", "start_node": "office_4", "end_node": "corridor_2", "distance": 3.605551275463989, "bidirectional": true, "speed_limit": 0.5, "edge_type": "corridor", "semantic_class": "corridor", "weight": 3.605551275463989, "penalty": 0.0}}, {"type": "Feature", "geometry": {"type": "LineString", "coordinates": [[13.0, 2.0], [10.0, 0.0]]}, "properties": {"id": "edge_19", "start_node": "meeting_room", "end_node": "corridor_2", "distance": 3.605551275463989, "bidirectional": true, "speed_limit": 0.5, "edge_type": "corridor", "semantic_class": "corridor", "weight": 3.605551275463989, "penalty": 0.0}}, {"type": "Feature", "geometry": {"type": "LineString", "coordinates": [[1.0, -2.0], [0.0, 0.0]]}, "properties": {"id": "edge_20", "start_node": "charging_station", "end_node": "entrance", "distance": 2.23606797749979, "bidirectional": true, "speed_limit": 0.5, "edge_type": "corridor", "semantic_class": "entrance", "weight": 2.23606797749979, "penalty": 0.0}}]}