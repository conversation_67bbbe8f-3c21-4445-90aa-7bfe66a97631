# Nav2 Route Server配置文件
# 用于fusion项目的路网导航功能
# {{ AURA-X: Create - Route Server配置，基于Nav2官方标准和fusion项目需求. Approval: 寸止(ID:route_integration_phase1). }}

route_server:
  ros__parameters:
    # === 基础配置 ===
    use_sim_time: false                       # 使用真实时间
    base_frame: "base_footprint"              # 机器人基础坐标系（标准名称，命名空间由PushRosNamespace自动添加）
    route_frame: "map"                        # 路网图参考坐标系
    
    # === 路径生成配置 ===
    path_density: 0.05                        # 路径点密度(m)，用于生成nav_msgs/Path
    max_iterations: 0                         # 最大搜索迭代次数，0表示使用最大可能值
    max_planning_time: 2.0                    # 最大规划时间(秒)
    
    # === 路径平滑配置 ===
    smooth_corners: true                      # 是否平滑路径转角
    smoothing_radius: 0.8                     # 转角平滑半径(m)，适配fusion机器人尺寸
    
    # === 图文件配置 ===
    graph_filepath: ""                        # 图文件路径，将由launch文件动态设置
    graph_file_loader: "GeoJsonGraphFileLoader"  # 图文件加载器
    graph_file_loader:
      plugin: "nav2_route::GeoJsonGraphFileLoader"
    
    # === 代价地图配置 ===
    costmap_topic: "global_costmap/costmap_raw"  # 全局代价地图话题
    
    # === 路径跟踪配置 ===
    tracker_update_rate: 50.0                 # 路径跟踪更新频率(Hz)
    aggregate_blocked_ids: false              # 是否聚合阻塞的边ID
    
    # === 节点到达判定配置 ===
    boundary_radius_to_achieve_node: 1.0      # 边界节点（起点/终点）到达半径(m)
    radius_to_achieve_node: 1.5               # 普通节点到达半径(m)
    
    # === 路径修剪配置 ===
    max_prune_dist_from_edge: 8.0             # 距离边的最大修剪距离(m)
    min_prune_dist_from_goal: 0.15            # 距离目标的最小修剪距离(m)
    min_prune_dist_from_start: 0.10           # 距离起点的最小修剪距离(m)
    prune_goal: true                          # 是否允许修剪目标节点
    
    # === 最近邻搜索配置 ===
    enable_nn_search: true                    # 启用广度优先搜索找最近可达节点
    max_nn_search_iterations: 10000          # 最大搜索迭代次数
    num_nearest_nodes: 5                     # K-d树最近邻节点数量
    
    # === 边权重评分插件 ===
    edge_cost_functions: ["DistanceScorer", "DynamicEdgesScorer", "CostmapScorer"]
    
    # 距离评分器 - 基于边长度
    DistanceScorer:
      plugin: "nav2_route::DistanceScorer"
      weight: 1.0                             # 权重
      speed_tag: "speed_limit"                # 速度限制标签
    
    # 动态边评分器 - 支持运行时动态调整
    DynamicEdgesScorer:
      plugin: "nav2_route::DynamicEdgesScorer"
    
    # 代价地图评分器 - 基于代价地图的边评分
    CostmapScorer:
      plugin: "nav2_route::CostmapScorer"
      weight: 2.0                             # 权重
      costmap_topic: "global_costmap/costmap_raw"
      max_cost: 253.0                         # 最大代价值
      use_maximum: true                       # 使用最大值而非平均值
      invalid_on_collision: true              # 碰撞时标记为无效
      invalid_off_map: true                   # 超出地图时标记为无效
      check_resolution: 1                     # 检查分辨率（1=代价地图分辨率）
    
    # === 路径操作插件 ===
    operations: ["AdjustSpeedLimit", "CollisionMonitor", "ReroutingService"]
    
    # 速度限制调整器
    AdjustSpeedLimit:
      plugin: "nav2_route::AdjustSpeedLimit"
      speed_limit_topic: "speed_limit"        # 速度限制话题
      speed_tag: "speed_limit"                # 图中速度标签
    
    # 碰撞监控器
    CollisionMonitor:
      plugin: "nav2_route::CollisionMonitor"
      costmap_topic: "global_costmap/costmap_raw"
      rate: 2.0                               # 检查频率(Hz)
      max_cost: 253.0                         # 最大允许代价
      max_collision_dist: 5.0                 # 前瞻碰撞检查距离(m)
      check_resolution: 2                     # 检查分辨率倍数
      reroute_on_collision: true              # 碰撞时重新路由
    
    # 重路由服务
    ReroutingService:
      plugin: "nav2_route::ReroutingService"
    
    # === 服务内省配置 ===
    introspection_mode: "disabled"            # 服务内省模式
    allow_parameter_qos_overrides: true       # 允许QoS参数覆盖
