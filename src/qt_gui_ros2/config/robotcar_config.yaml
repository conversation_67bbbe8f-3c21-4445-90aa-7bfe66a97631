# ========================================
# RobotCar Qt GUI 配置文件
# ========================================
# 适配robotcar导航系统的Qt GUI参数配置
# 作者: Augment Agent
# 日期: 2025-07-15

# ========== 基本参数 ==========
qt_gui_ros2:
  ros__parameters:
    # 时间配置
    use_sim_time: false                    # 使用真实时间，非仿真模式
    
    # ========== 坐标系配置 ==========
    # 与robotcar导航系统保持一致
    map_frame: "map"                       # 地图坐标系，来自Cartographer
    odom_frame: "odom"                     # 里程计坐标系
    base_frame: "base_footprint"           # 机器人本体坐标系
    laser_frame: "laser_link"              # 激光雷达坐标系
    imu_frame: "imu_link"                  # IMU坐标系
    
    # ========== 话题配置 ==========
    # 适配robotcar系统的话题名称
    topics:
      # 控制话题
      cmd_vel: "/diff_drive_controller/cmd_vel_unstamped"
      
      # 传感器话题
      laser_scan: "/scan"                  # 激光雷达扫描数据
      imu: "/imu_broadcaster/imu"                         # IMU数据
      
      # 导航话题
      map: "/map"                         # 地图数据，来自Cartographer
      odom_raw: "/diff_drive_controller/odom"      # 原始里程计数据
      odom_filtered: "/odometry/filtered"  # EKF融合后的里程计数据
      
      # 导航目标话题
      goal_pose: "/goal_pose"             # 导航目标位置
      move_base_goal: "/move_base_simple/goal"  # 简单导航目标
      
    # ========== 机器人控制参数 ==========
    robot_control:
      # 线速度控制参数
      linear_velocity:
        max_speed: 0.5                    # 最大线速度 (m/s)
        default_speed: 0.2                # 默认线速度 (m/s)
        min_speed: 0.05                   # 最小线速度 (m/s)
        
      # 角速度控制参数  
      angular_velocity:
        max_speed: 1.0                    # 最大角速度 (rad/s)
        default_speed: 0.3                # 默认角速度 (rad/s)
        min_speed: 0.1                    # 最小角速度 (rad/s)
        
      # 安全参数
      safety:
        emergency_stop_timeout: 1.0       # 紧急停止超时时间 (s)
        command_timeout: 0.5              # 命令超时时间 (s)
        
    # ========== RViz显示配置 ==========
    rviz_config:
      # 网格显示
      grid:
        enabled: true                     # 启用网格显示
        cell_count: 50                    # 网格单元数量
        cell_size: 1.0                    # 网格单元大小 (m)
        color: [0.5, 0.5, 0.5]           # 网格颜色 (RGB)
        
      # TF显示
      tf:
        enabled: true                     # 启用TF显示
        show_names: true                  # 显示坐标系名称
        show_axes: true                   # 显示坐标轴
        show_arrows: true                 # 显示箭头
        
      # 地图显示
      map:
        enabled: true                     # 启用地图显示
        topic: "/map"                     # 地图话题
        color_scheme: "map"               # 颜色方案
        alpha: 0.7                        # 透明度
        
      # 激光雷达显示
      laser_scan:
        enabled: true                     # 启用激光扫描显示
        topic: "/scan"                    # 激光扫描话题
        size: 0.05                        # 点大小
        color: [1.0, 0.0, 0.0]           # 点颜色 (红色)
        decay_time: 0.0                   # 衰减时间
        
      # 机器人模型显示
      robot_model:
        enabled: true                     # 启用机器人模型显示
        description_topic: "/robot_description"  # 机器人描述话题
        visual_enabled: true              # 启用视觉模型
        collision_enabled: false          # 禁用碰撞模型
        
    # ========== GUI界面配置 ==========
    gui_config:
      # 窗口设置
      window:
        title: "RobotCar Navigation Control"  # 窗口标题
        width: 1200                       # 窗口宽度
        height: 800                       # 窗口高度
        
      # 控制按钮设置
      control_buttons:
        size: [80, 40]                    # 按钮大小 [宽度, 高度]
        spacing: 10                       # 按钮间距
        
      # 状态指示器设置
      status_indicators:
        update_rate: 20.0                 # 状态更新频率 (Hz)
        
    # ========== 机器人物理参数 ==========
    # 与robotcar硬件规格保持一致
    robot_specs:
      # 几何参数
      geometry:
        wheelbase: 0.34                   # 轮距 (m)
        wheel_radius: 0.08                # 车轮半径 (m)
        robot_length: 0.70                # 机器人长度 (m)
        robot_width: 0.56                 # 机器人宽度 (m)
        
      # 性能参数
      performance:
        max_linear_velocity: 0.5          # 最大线速度 (m/s)
        max_angular_velocity: 1.0         # 最大角速度 (rad/s)
        max_acceleration: 0.5             # 最大加速度 (m/s²)
        
    # ========== 传感器配置 ==========
    sensors:
      # 激光雷达配置
      lidar:
        frequency: 25.0                   # 扫描频率 (Hz)
        range_min: 0.15                   # 最小测距 (m)
        range_max: 12.0                   # 最大测距 (m)
        
      # IMU配置  
      imu:
        frequency: 100.0                  # 数据频率 (Hz)
        frame_id: "imu_link"             # IMU坐标系
        
      # 里程计配置
      odometry:
        frequency: 50.0                   # 数据频率 (Hz)
        frame_id: "odom"                 # 里程计坐标系
